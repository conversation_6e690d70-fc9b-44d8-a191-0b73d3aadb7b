#!/usr/bin/env ruby
# frozen_string_literal: true

# Script to configure bundler to use version-specific lock files
# This allows testing against different Ruby/Rails combinations with separate lock files

require 'fileutils'

class BundleConfigurator
  def initialize
    @project_root = File.expand_path('..', __dir__)
    @bundle_dir = File.join(@project_root, '.bundle')
  end

  def configure
    ruby_version = detect_ruby_version
    rails_version = detect_rails_version

    puts "Configuring bundler for Ruby #{ruby_version} and Rails #{rails_version}"

    ensure_bundle_directory
    create_bundle_config(ruby_version, rails_version)

    puts "✅ Bundler configured to use lockfile: .bundle/Gemfile.#{ruby_version}-#{rails_version}.lock"
  end

  private

  def detect_ruby_version
    # Use major.minor version (e.g., "3.1", "2.7")
    RUBY_VERSION.split('.')[0..1].join('.')
  end

  def detect_rails_version
    # Get Rails version from environment variable or use default
    rails_version = ENV['RAILS_VERSION']
    rails_version = '8.0.0' if rails_version.nil?

    # Extract major.minor version (e.g., "8.0", "7.1")
    Gem::Version.new(rails_version).segments[0..1].join('.')
  end

  def ensure_bundle_directory
    FileUtils.mkdir_p(@bundle_dir) unless Dir.exist?(@bundle_dir)
  end

  def create_bundle_config(ruby_version, rails_version)
    lockfile_name = "Gemfile.#{ruby_version}-#{rails_version}.lock"
    lockfile_path = ".bundle/#{lockfile_name}"

    config_path = File.join(@bundle_dir, 'config')

    puts "   DEBUG: lockfile_path = #{lockfile_path.inspect}"
    puts "   DEBUG: config_path = #{config_path.inspect}"

    # Create bundler config file with lockfile setting
    config_content = <<~CONFIG
      ---
      BUNDLE_LOCKFILE: "#{lockfile_path}"
    CONFIG

    puts "   DEBUG: config_content = #{config_content.inspect}"

    File.write(config_path, config_content)
    puts "   Created .bundle/config with lockfile: #{lockfile_path}"

    # Verify the file was written correctly
    written_content = File.read(config_path)
    puts "   DEBUG: written content = #{written_content.inspect}"
  end
end

if __FILE__ == $0
  BundleConfigurator.new.configure
end
