#!/usr/bin/env ruby
# frozen_string_literal: true

# Dynamic bundler wrapper that sets version-specific lockfile paths
# This allows testing against different Ruby/Rails combinations with separate lock files

require 'fileutils'

def detect_ruby_version
  # Use major.minor version (e.g., "3.1", "2.7")
  RUBY_VERSION.split('.')[0..1].join('.')
end

def detect_rails_version
  # Get Rails version from environment variable or use default
  rails_version = ENV['RAILS_VERSION']
  rails_version = '8.0.0' if rails_version.nil?
  
  # Extract major.minor version (e.g., "8.0", "7.1")
  Gem::Version.new(rails_version).segments[0..1].join('.')
end

def get_lockfile_path
  ruby_version = detect_ruby_version
  rails_version = detect_rails_version
  
  project_root = File.expand_path('..', __dir__)
  bundle_dir = File.join(project_root, '.bundle')
  
  # Ensure .bundle directory exists
  FileUtils.mkdir_p(bundle_dir) unless Dir.exist?(bundle_dir)
  
  lockfile_name = "Gemfile.#{ruby_version}-#{rails_version}.lock"
  File.join(bundle_dir, lockfile_name)
end

# Set the dynamic lockfile path
ENV['BUNDLE_LOCKFILE'] = get_lockfile_path

# Find the real bundler executable
bundler_path = `which -a bundle`.split("\n").find { |path| path != __FILE__ }

if bundler_path.nil?
  puts "❌ Could not find bundler executable"
  exit 1
end

# Execute the real bundler with all arguments
exec(bundler_path, *ARGV)
